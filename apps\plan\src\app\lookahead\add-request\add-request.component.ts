import {
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  inject,
  SimpleChanges,
  OnChanges,
  ChangeDetectorRef,
  HostListener,
  OnDestroy,
  DestroyRef,
  effect,
  ChangeDetectionStrategy,
  computed,
  signal,
} from '@angular/core';
import { Async<PERSON>ip<PERSON>, NgIf, NgClass, CommonModule } from '@angular/common';
import {
  FormControl,
  ReactiveFormsModule,
  Validators,
  FormsModule,
  FormGroup,
} from '@angular/forms';
import { Subscription, startWith } from 'rxjs';
import { map } from 'rxjs/operators';
import { RequirementComponent } from './requirement/requirement.component';
import { DatePipe } from '@angular/common';
import { MatDialog } from '@angular/material/dialog';
import { Store } from '@ngrx/store';
import { lookaheadFeature } from '../../../../../../libs/services/src/lib/services/lookahead/store/features';
import { ClientLocation } from 'libs/services/src/lib/services/client-locations/interfaces/client-locations.interface';
import { Asset } from 'libs/services/src/lib/services/maintenance/interfaces/asset.interface';
import {
  timeAfter,
  timeBefore,
} from '../../../../../../libs/components/src/lib/functions/utility.functions';
import { LookaheadSharedService } from 'libs/services/src/lib/services/shared/lookahead-subject.service';
import { LookaheadActions } from '../../../../../../libs/services/src/lib/services/lookahead/store/actions/lookahead.action';
import { SailingRequest } from '../../../../../../libs/services/src/lib/services/lookahead/interfaces/sailing-request.interface';
import { SailingRequestAsset } from '../../../../../../libs/services/src/lib/services/lookahead/interfaces/sailing-request-asset.interface';
import { ActivitySelection } from '../../../../../../libs/services/src/lib/services/lookahead/interfaces/activity-selection.interface';
import { confirmActions } from '../../../../../../libs/components/src/lib/store/confirm.actions';
import { planningDetailsFeature } from '../../../../../../libs/services/src/lib/services/voyages/store/features/planning-details.feature';
import { Actions } from '@ngrx/effects';
import { PlanningDetailsService } from '../../shared/services/planning-details.service';
import { SidebarModule } from 'primeng/sidebar';
import { currentUserFeature } from '../../../../../../libs/auth/src/lib/store/current-user/current-user.features';
import { DropdownModule } from 'primeng/dropdown';
import { TabViewModule } from 'primeng/tabview';
import { SpinnerModule } from 'primeng/spinner';
import { vesselsFeature } from '../../../../../../libs/services/src/lib/services/vessels/store/features/vessels.features';
import { ButtonModule } from 'primeng/button';
import { MultiSelectModule } from 'primeng/multiselect';
import { CalendarModule } from 'primeng/calendar';
import { TooltipModule } from 'primeng/tooltip';
import { Voyage } from '../../../../../../libs/services/src/lib/services/voyages/interfaces/voyage.interface';
import { RepeatDialogData } from '../../shared/types/repeat-dialog-data.interface';
import { RepeatDialogActions } from '../../store/actions/repeat-dialog.actions';
import { repeatDialogFeature } from '../../store/features/repeat-dialog.feature';
import { ConfirmationService } from 'primeng/api';
import { InputTextModule } from 'primeng/inputtext';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { ActivityCategory } from 'libs/services/src/lib/services/maintenance/interfaces/activity-category.interface';
import { schedulePanelFeature } from '../../../../../../libs/services/src/lib/services/lookahead/store/features/schedule-panel.feature';
import { SchedulePanelActions } from '../../../../../../libs/services/src/lib/services/lookahead/store/actions/schedule-panel.action';

@Component({
  selector: 'add-request',
  templateUrl: './add-request.component.html',
  styleUrls: ['./add-request.component.scss'],
  standalone: true,
  imports: [
    NgIf,
    ReactiveFormsModule,
    FormsModule,
    RequirementComponent,
    NgClass,
    CommonModule,
    SidebarModule,
    DropdownModule,
    TabViewModule,
    SpinnerModule,
    ButtonModule,
    MultiSelectModule,
    CalendarModule,
    TooltipModule,
    InputTextModule,
    ProgressSpinnerModule,
  ],
  providers: [DatePipe],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AddRequestComponent implements OnInit, OnDestroy, OnChanges {
  cdr = inject(ChangeDetectorRef);
  datePipe = inject(DatePipe);
  dialog = inject(MatDialog);
  store = inject(Store);
  private actions = inject(Actions);
  private destroyRef = inject(DestroyRef);
  private confirmationService = inject(ConfirmationService);
  sharedService = inject(LookaheadSharedService);
  private planningDetailsService = inject(PlanningDetailsService);
  lookaheadState = this.store.selectSignal(
    lookaheadFeature.selectLookaheadsState
  );

  isVisibleSchedulePanel = this.store.selectSignal(
    schedulePanelFeature.selectIsVisibleSchedulePanel
  );
  inboundPlanningDetails = this.store.selectSignal(
    planningDetailsFeature.selectInboundPlanningDetails
  );
  outboundPlanningDetails = this.store.selectSignal(
    planningDetailsFeature.selectOutboundPlanningDetails
  );
  originalClients = this.store.selectSignal(
    lookaheadFeature.selectLocationClients
  );
  originalAssets = this.store.selectSignal(lookaheadFeature.selectAssets);
  activityCategories = this.store.selectSignal(
    lookaheadFeature.selectActivityCategories
  );
  units = this.store.selectSignal(lookaheadFeature.selectUnits);
  currentUser = this.store.selectSignal(currentUserFeature.selectUser);
  vessels = this.store.selectSignal(vesselsFeature.selectVessels);
  selectedInboundVoyage = this.store.selectSignal(
    lookaheadFeature.selectSelectedInboundVoyage
  );
  selectedOutboundVoyage = this.store.selectSignal(
    lookaheadFeature.selectSelectedOutboundVoyage
  );
  panelSailingRequest = this.store.selectSignal(
    schedulePanelFeature.selectPanelSailingRequest
  );
  voyages = this.store.selectSignal(lookaheadFeature.selectVoyages);
  repeatDialogData = this.store.selectSignal(repeatDialogFeature.selectResult);

  inboundOutboundVoyageSelected = computed(() => {
    return (
      !this.editMode &&
      (this.form.get('inboundVoyageId')?.value !== null ||
        this.form.get('outboundVoyageId')?.value !== null)
    );
  });

  // Image paths for inbound button
  readonly inboundNormalImg = 'assets/inbound.svg';
  readonly inboundSelectedImg = 'assets/inbound-selected.svg';
  readonly inboundSelectedActiveImg = 'assets/inbound-selected-active.svg';
  readonly inboundHoverImg = 'assets/inbound-hover.svg';

  // Image paths for outbound button
  readonly outboundNormalImg = 'assets/outbound.svg';
  readonly outboundSelectedImg = 'assets/outbound-selected.svg';
  readonly outboundSelectedActiveImg = 'assets/outbound-selected-active.svg';
  readonly outboundHoverImg = 'assets/outbound-hover.svg';

  // Computed properties for image sources
  inboundImageSrc = computed(() => {
    const isSelected =
      this.inboundVoyageIdValue() !== null &&
      this.inboundVoyageIdValue() !== '';

    if (isSelected && this.inboundVoyageActive) {
      return this.inboundSelectedActiveImg;
    }
    if (this.inboundVoyageActive && !isSelected) {
      return this.inboundHoverImg;
    }
    if (!this.inboundVoyageActive && isSelected) {
      return this.inboundSelectedImg;
    }
    if (this.inboundVoyageHover()) {
      return this.inboundHoverImg;
    }
    return this.inboundNormalImg;
  });

  outboundImageSrc = computed(() => {
    const isSelected =
      this.outboundVoyageIdValue() !== null &&
      this.outboundVoyageIdValue() !== '';

    if (isSelected && this.outboundVoyageActive) {
      return this.outboundSelectedActiveImg;
    }
    if (this.outboundVoyageActive && !isSelected) {
      return this.outboundHoverImg;
    }
    if (!this.outboundVoyageActive && isSelected) {
      return this.outboundSelectedImg;
    }
    if (this.outboundVoyageHover()) {
      return this.outboundHoverImg;
    }
    return this.outboundNormalImg;
  });

  @Input() inboundVoyageSelected?: string | null;
  @Input() outboundVoyageSelected?: string | null;
  @Input() startDate?: Date | null = null;
  @Input() endDate?: Date | null = null;
  @Output() resetFormChange = new EventEmitter();
  @Output() isInboundOutboundSidenavOpenedChange = new EventEmitter<{
    isOpen: boolean;
    voyageType: string;
    vesselId: string;
  }>();
  clients: ClientLocation[] = [];
  assets: Asset[] = [];
  minStartDate!: Date;
  outboundVoyages: Voyage[] = [];
  inboundVoyages: Voyage[] = [];
  filteredActivities: ActivityCategory[] = [];

  headerText = 'Single occurrence';

  private subscriptions = new Subscription();

  selectedTabIndex = 0;
  form = new FormGroup({
    clientId: new FormControl<string>('', [Validators.required]),
    vesselId: new FormControl<string>('', [Validators.required]),
    sailingRequestAssets: new FormControl<string[]>([]),
    startTime: new FormControl<Date | null>(null, [Validators.required]),
    endTime: new FormControl<Date | null>(null, [Validators.required]),
    seriesStartTime: new FormControl<Date | null>(null),
    seriesEndTime: new FormControl<Date | null>(null),
    eta: new FormControl<string>(''),
    etd: new FormControl<string>(''),
    inboundVoyageId: new FormControl<string | null>(null),
    outboundVoyageId: new FormControl<string | null>(null),
    doesRepeat: new FormControl<string>(''),
    repeatEveryNumberOfWeeks: new FormControl<number | null>(null),
    weeklyPattern: new FormControl<string | null>(null),
  });
  searchClientControl = new FormControl('');
  searchAssetControl = new FormControl('');
  activitiesSelected: ActivitySelection | undefined;
  inboundVoyageActive = false;
  outboundVoyageActive = false;
  inboundVoyageHover = signal(false);
  outboundVoyageHover = signal(false);

  // Signals to track form control values for computed properties
  inboundVoyageIdValue = signal<string | null>(null);
  outboundVoyageIdValue = signal<string | null>(null);

  timeAfter: Date | null = null;
  timeBefore: Date | null = null;
  minDifference = 1;
  today = new Date();
  editMode = false;
  sailingRequestId = '';

  repeatOptions = [
    { label: 'Does Not Repeat', value: 'DoesNotRepeat' },
    { label: 'Does Repeat', value: 'DoesRepeat' },
  ];

  constructor() {
    effect(
      () => {
        const repeatSchedule = this.repeatDialogData();
        if (repeatSchedule && !this.editMode) {
          const startDate = repeatSchedule.startDate
            ? new Date(repeatSchedule.startDate)
            : null;
          const endDate = repeatSchedule.endDate
            ? new Date(repeatSchedule.endDate)
            : null;

          this.form.patchValue(
            {
              startTime: startDate,
              endTime: endDate,
              seriesStartTime: startDate,
              seriesEndTime: endDate,
              weeklyPattern: repeatSchedule.weeklyPattern,
              repeatEveryNumberOfWeeks: repeatSchedule.repeatEveryNumberOfWeeks,
              doesRepeat: 'DoesRepeat',
            },
            { emitEvent: false }
          );
        }
      },
      { allowSignalWrites: true }
    );

    effect(
      () => {
        const request = this.panelSailingRequest();
        if (request) {
          this.editMode = true;
          this.sailingRequestId = request.Id;
          this.patchForm(request);
        } else {
          this.editMode = false;
          this.resetForm();
        }
      },
      { allowSignalWrites: true }
    );

    effect(
      () => {
        const inboundVoyage = this.selectedInboundVoyage();
        if (inboundVoyage !== null) {
          this.form.get('inboundVoyageId')?.setValue(inboundVoyage);
        }
      },
      { allowSignalWrites: true }
    );

    effect(
      () => {
        const outboundVoyage = this.selectedOutboundVoyage();
        if (outboundVoyage !== null) {
          this.form.get('outboundVoyageId')?.setValue(outboundVoyage);
        }
      },
      { allowSignalWrites: true }
    );
  }

  ngOnInit() {
    this.subToSubjects();

    this.updateMinStartDate();
    this.disableAssetControl();
    this.loadPlanningDetails();
    this.filters();

    // Subscribe to form control changes to update signals for computed properties
    this.form.get('inboundVoyageId')?.valueChanges.subscribe((value) => {
      this.inboundVoyageIdValue.set(value);
    });
    this.form.get('outboundVoyageId')?.valueChanges.subscribe((value) => {
      this.outboundVoyageIdValue.set(value);
    });

    // Set initial values
    this.inboundVoyageIdValue.set(
      this.form.get('inboundVoyageId')?.value ?? null
    );
    this.outboundVoyageIdValue.set(
      this.form.get('outboundVoyageId')?.value ?? null
    );
  }

  closeSideBar(visible: boolean) {
    if (!visible) {
      if (this.form.dirty) {
        this.openConfirmationDialog();
      } else {
        this.form.reset();
        this.editMode = false;
        this.sharedService.resetSailingForm();
        this.store.dispatch(
          SchedulePanelActions.change_Visibility_Schedule_Panel({ visible })
        );
      }
    }
  }

  updateMinStartDate() {
    if (this.editMode) {
      const startTimeValue = this.form.get('startTime')?.value;
      if (startTimeValue instanceof Date) {
        this.minStartDate = startTimeValue;
      } else if (typeof startTimeValue === 'string') {
        const parsedDate = new Date(startTimeValue);
        if (!isNaN(parsedDate.getTime())) {
          this.minStartDate = parsedDate;
        } else {
          this.minStartDate = new Date();
        }
      } else {
        this.minStartDate = new Date();
      }
    } else {
      this.minStartDate = new Date();
    }
  }

  loadPlanningDetails() {
    this.planningDetailsService
      .loadInboundPlanningDetails(this.destroyRef)
      .subscribe((planningDetails) => {
        if (planningDetails !== null) {
          const etaDate = new Date(planningDetails.eta!);
          const etaTimeString: string = etaDate.toTimeString().slice(0, 5);

          this.form.patchValue({
            startTime: etaDate,
            eta: etaTimeString,
          });
        }
      });

    this.planningDetailsService
      .loadOutboundPlanningDetails(this.destroyRef)
      .subscribe((planningDetails) => {
        if (planningDetails !== null) {
          const etdDate = new Date(planningDetails.etd!);
          const etdTimeString: string = etdDate.toTimeString().slice(0, 5);

          this.form.patchValue({
            endTime: etdDate,
            etd: etdTimeString,
          });
        }
      });
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  subToSubjects() {
    this.subscriptions.add(
      this.sharedService.resetSailingFormSubject.subscribe(() => {
        this.clearAndCloseForm();
      })
    );
    this.subscriptions.add(
      this.sharedService.closeInboundOutboundSidenavSubject.subscribe(() => {
        this.inboundVoyageActive = false;
        this.outboundVoyageActive = false;
      })
    );
    this.subscriptions.add(
      this.sharedService.updateRequestedInboundVoyageSubject.subscribe(
        (data) => {
          this.inboundVoyageSelected = data;
          this.form.get('inboundVoyageId')?.setValue(data);
        }
      )
    );
    this.subscriptions.add(
      this.sharedService.updateRequestedOutboundVoyageSubject.subscribe(
        (data) => {
          this.outboundVoyageSelected = data;
          this.form.get('outboundVoyageId')?.setValue(data);
        }
      )
    );
    this.subscriptions.add(
      this.sharedService.editNonPoolSailingRequestSubject.subscribe((data) => {
        this.editMode = true;
        this.sailingRequestId = data.Id;
        if (data.SeriesStartTime !== null && data.SeriesEndTime !== null) {
          this.headerText = 'Series';
          this.form.controls.doesRepeat.setValue('DoesRepeat');
          this.form.controls.seriesStartTime.setValue(data.SeriesStartTime);
          this.form.controls.seriesEndTime.setValue(data.SeriesEndTime);
        }
        this.patchForm(data);
      })
    );
    this.subscriptions.add(
      this.sharedService.updateScheduledInboundVoyageSubject.subscribe(
        (data) => {
          this.inboundVoyageSelected = data;
          this.form.get('inboundVoyageId')?.setValue(data);
        }
      )
    );
    this.subscriptions.add(
      this.sharedService.updateScheduledOutboundVoyageSubject.subscribe(
        (data) => {
          this.outboundVoyageSelected = data;
          this.form.get('outboundVoyageId')?.setValue(data);
        }
      )
    );

    this.subscriptions.add(
      this.form.get('outboundVoyageId')?.valueChanges.subscribe((value) => {
        if (value !== null) {
          this.updateFilteredCategories();
        }
      })
    );

    this.subscriptions.add(
      this.form.get('inboundVoyageId')?.valueChanges.subscribe((value) => {
        if (value !== null) {
          this.updateFilteredCategories();
        }
      })
    );
  }

  private updateFilteredCategories() {
    const inboundVoyageId = this.form.get('inboundVoyageId')?.value;
    const outboundVoyageId = this.form.get('outboundVoyageId')?.value;

    if (inboundVoyageId && !outboundVoyageId) {
      this.filteredActivities = this.activityCategories()!
        .map((category: any) => {
          const filteredTypes = category.activityCategoryTypes.filter(
            (type: any) => !type.createOutOfPortActivity
          );
          return {
            ...category,
            activityCategoryTypes: filteredTypes,
          };
        })
        .filter((category: any) => category.activityCategoryTypes.length > 0);
    } else {
      this.filteredActivities = this.activityCategories()!;
    }
  }

  private patchForm(sailingRequest: any): void {
    const etaValue = sailingRequest.Eta
      ? typeof sailingRequest.Eta === 'string'
        ? this.convertTimeStringToDate(sailingRequest.Eta)
        : sailingRequest.Eta
      : null;

    const etdValue = sailingRequest.Etd
      ? typeof sailingRequest.Etd === 'string'
        ? this.convertTimeStringToDate(sailingRequest.Etd)
        : sailingRequest.Etd
      : null;

    const sailingRequestAssets = sailingRequest.SailingRequestAssets ?? [];
    this.inboundVoyageSelected = sailingRequest.InboundVoyageId;
    this.outboundVoyageSelected = sailingRequest.OutboundVoyageId;
    this.form.patchValue({
      clientId: sailingRequest.ClientId,
      vesselId: sailingRequest.VesselId,
      sailingRequestAssets: sailingRequestAssets.map(
        (asset: any) => asset.assetId
      ),
      startTime: sailingRequest.StartTime
        ? new Date(sailingRequest.StartTime)
        : new Date(),
      endTime: sailingRequest.EndTime ? new Date(sailingRequest.EndTime) : null,
      eta: etaValue,
      etd: etdValue,
      inboundVoyageId: sailingRequest.InboundVoyageId,
      outboundVoyageId: sailingRequest.OutboundVoyageId,
      weeklyPattern: sailingRequest.WeeklyPattern,
    });

    this.activitiesSelected = {
      activities: sailingRequest.SailingRequestActivities,
      pilotActive: sailingRequest.Pilot,
      lightDuesActive: sailingRequest.LightDues,
    };

    // Update signals for computed properties
    this.inboundVoyageIdValue.set(sailingRequest.InboundVoyageId);
    this.outboundVoyageIdValue.set(sailingRequest.OutboundVoyageId);

    this.changeStartDate(sailingRequest.StartTime);
    this.changeEndDate(sailingRequest.EndTime);
  }

  private convertTimeStringToDate(timeString: string): Date {
    const [hours, minutes] = timeString.split(':').map(Number);
    const date = new Date();
    date.setHours(hours, minutes, 0, 0);
    return date;
  }

  ngOnChanges(changes: SimpleChanges) {
    if (
      changes['originalClients'] &&
      !changes['originalClients'].isFirstChange()
    ) {
      this.clients = Object.assign([], this.originalClients());
      this.filters();
    }
    if (
      changes['originalAssets'] &&
      !changes['originalAssets'].isFirstChange()
    ) {
      this.assets = Object.assign([], this.originalAssets());
      this.filters();
    }
    if (changes['inboundSelection'] || changes['outboundSelection']) {
      if (changes['inboundSelection']) {
        this.form.get('inboundVoyageId')?.setValue(null);
      }
      if (changes['outboundSelection']) {
        this.form.get('outboundVoyageId')?.setValue(null);
      }
    }
  }

  private filters(): void {
    this.searchClientControl.valueChanges
      .pipe(
        startWith(''),
        map((value) => this._filterClients(value!))
      )
      .subscribe((filteredClients) => {
        this.clients = filteredClients;
      });

    this.searchAssetControl.valueChanges
      .pipe(
        startWith(''),
        map((value) => this._filterAssets(value!))
      )
      .subscribe((filteredAssets) => {
        this.assets = filteredAssets;
      });
    this.cdr.detectChanges();
  }

  private _filterClients(value: string): ClientLocation[] {
    if (value === '') {
      return this.originalClients() ?? [];
    }

    const filterValue = value.toLowerCase();
    const numericFilterValue = value.toString().toLowerCase();

    return this.originalClients()!.filter(
      (client) =>
        client.clientName.toLowerCase().includes(filterValue) ||
        client.clientName.toLowerCase().includes(numericFilterValue)
    );
  }

  private _filterAssets(value: string | null): Asset[] {
    if (value === '') {
      return this.originalAssets() ?? [];
    }

    const filterValue = value!.toLowerCase();
    const numericFilterValue = value!.toString().toLowerCase();

    return this.originalAssets()!.filter(
      (asset) =>
        asset.name.toLowerCase().includes(filterValue) ||
        asset.name.toLowerCase().includes(numericFilterValue)
    );
  }

  disableAssetControl() {
    this.form.get('clientId')?.valueChanges.subscribe((clientId) => {
      if (clientId) {
        this.form.get('sailingRequestAssets')?.enable();
      } else {
        this.form.get('sailingRequestAssets')?.disable();
      }
    });
  }

  vesselNotSelectedOrSeriesSelected(): boolean {
    return (
      !this.form.get('vesselId')?.value ||
      (!this.editMode &&
        this.form.value.seriesStartTime !== null &&
        this.form.value.seriesEndTime !== null)
    );
  }

  toggleRequest() {
    if (this.form.dirty) {
      this.openConfirmationDialog();
    } else {
      this.store.dispatch(
        SchedulePanelActions.change_Visibility_Schedule_Panel({
          visible: false,
        })
      );
      this.sharedService.resetSailingForm();
    }
  }

  clearAndCloseForm() {
    this.selectedTabIndex = 0;
    this.resetForm();
    setTimeout(() => {
      this.editMode = false;
    }, 500);
    this.store.dispatch(
      LookaheadActions.update_Selected_Inbound_Voyage({ voyageId: '' })
    );
    this.store.dispatch(
      LookaheadActions.update_Selected_Outbound_Voyage({ voyageId: '' })
    );

    this.store.dispatch(
      SchedulePanelActions.change_Visibility_Schedule_Panel({ visible: false })
    );
  }

  openConfirmationDialog() {
    this.confirmationService.confirm({
      message:
        'You have unsaved changes. Are you sure you want to proceed without saving?',
      header: 'Unsaved Changes',
      rejectButtonStyleClass: 'btn-tertiary',
      acceptButtonStyleClass: 'btn-negative-primary',
      acceptIcon: 'none',
      rejectIcon: 'none',
      acceptLabel: 'Proceed',
      rejectLabel: 'Cancel',
      accept: () => {
        this.form.reset();
        this.sharedService.resetSailingForm();
        this.editMode = false;
        this.store.dispatch(
          SchedulePanelActions.change_Visibility_Schedule_Panel({
            visible: false,
          })
        );
      },
    });
  }

  openInboundOutboundSidebar(voyageType: string): void {
    const vesselId = this.form.get('vesselId')?.value!;

    if (voyageType === 'Inbound') {
      this.store.dispatch(
        LookaheadActions.update_Selected_Inbound_Voyage({
          voyageId: this.form.get('inboundVoyageId')!.value ?? '',
        })
      );
      this.inboundVoyages = this.voyages().filter(
        (v: any) => v.vesselId === vesselId && v.voyageDirection === 0
      );
      this.inboundVoyageActive = true;
      this.outboundVoyageActive = false;
    } else {
      this.store.dispatch(
        LookaheadActions.update_Selected_Outbound_Voyage({
          voyageId: this.form.get('outboundVoyageId')!.value ?? '',
        })
      );
      this.outboundVoyages = this.voyages().filter(
        (v: any) => v.vesselId === vesselId && v.voyageDirection === 1
      );
      this.inboundVoyageActive = false;
      this.outboundVoyageActive = true;
    }

    this.store.dispatch(
      LookaheadActions.change_Visibility_Inbound_Outbound_Panel({
        visible: true,
        voyageType: voyageType,
        vesselId: vesselId,
        inboundVoyages: this.inboundVoyages,
        outboundVoyages: this.outboundVoyages,
      })
    );
  }

  changeStartDate(value: Date | null | undefined): void {
    let startDate: Date;
    if (value instanceof Date) {
      startDate = value;
    } else if (typeof value === 'string') {
      startDate = new Date(value);
    } else if (typeof value === 'number') {
      startDate = new Date(value);
    } else {
      return;
    }

    if (value) {
      this.timeBefore = timeBefore(startDate!, { min: this.minDifference });
      this.form.controls.endTime.setValidators([
        Validators.required,
        Validators.min(startDate!.getTime()),
      ]);
      this.form.controls.endTime.updateValueAndValidity();
    }
  }

  changeEndDate(value: Date): void {
    let endDate: Date;
    if (value instanceof Date) {
      endDate = value;
    } else if (typeof value === 'string') {
      endDate = new Date(value);
    } else if (typeof value === 'number') {
      endDate = new Date(value);
    } else {
      return;
    }

    if (value) {
      this.timeAfter = timeAfter(endDate, { min: this.minDifference - 1 });
      this.form.controls.startTime.setValidators([
        Validators.required,
        Validators.max(endDate.getTime()),
      ]);
      this.form.controls.startTime.updateValueAndValidity();
    }
  }

  goToNextTab() {
    if (this.form.invalid) {
      this.form.markAllAsTouched();
      return;
    }
    this.selectedTabIndex = 1;
  }

  handleSailingRequestActivitiesSelected(selection: ActivitySelection): void {
    this.activitiesSelected = selection;
  }

  isClientControlInvalid(): boolean {
    return (
      this.form.controls.clientId.invalid && this.form.controls.clientId.touched
    );
  }

  isVesselControlInvalid(): boolean {
    return (
      this.form.controls.vesselId.invalid && this.form.controls.vesselId.touched
    );
  }

  setDoesRepeat(value: string) {
    this.headerText = value === 'DoesRepeat' ? 'Series' : 'Single occurrence';
    this.form.get('doesRepeat')?.setValue(value);
    if (value === 'DoesRepeat') {
      this.openRepeatDialog();
    } else {
      this.form.get('seriesStartTime')?.setValue(null);
      this.form.get('seriesEndTime')?.setValue(null);
      this.form.get('weeklyPattern')?.setValue('');
      this.form.get('repeatEveryNumberOfWeeks')?.setValue(null);
    }
  }

  updateInboundOutbound(): void {
    this.sharedService.updateScheduledInboundVoyage(null);
    this.sharedService.updateScheduledOutboundVoyage(null);
  }

  openRepeatDialog() {
    const data: RepeatDialogData = {
      startDate:
        this.form.value.seriesStartTime !== null &&
        this.form.value.seriesStartTime !== undefined
          ? this.form.value.seriesStartTime instanceof Date
            ? this.form.value.seriesStartTime.toISOString()
            : this.form.value.seriesStartTime
          : this.timeBefore
          ? new Date(this.timeBefore).toISOString()
          : null,
      endDate:
        this.form.value.seriesEndTime !== null &&
        this.form.value.seriesEndTime !== undefined
          ? this.form.value.seriesEndTime instanceof Date
            ? this.form.value.seriesEndTime.toISOString()
            : this.form.value.seriesEndTime
          : this.timeAfter
          ? new Date(this.timeAfter).toISOString()
          : new Date(new Date().getFullYear(), 11, 31).toISOString(),
      repeatEveryNumberOfWeeks: 0,
      weeklyPattern: this.form.value.weeklyPattern ?? '',
    };

    const safeData: RepeatDialogData = {
      startDate: data.startDate ?? null,
      endDate: data.endDate ?? null,
      repeatEveryNumberOfWeeks: data.repeatEveryNumberOfWeeks,
      weeklyPattern: data.weeklyPattern,
    };

    this.store.dispatch(
      RepeatDialogActions.openRepeatDialog({
        data: safeData,
      })
    );
  }

  resetForm() {
    this.form.reset({
      clientId: '',
      vesselId: '',
      sailingRequestAssets: [],
      startTime: null,
      endTime: null,
      eta: null,
      etd: null,

      inboundVoyageId: null,
      outboundVoyageId: null,
      doesRepeat: '',
      seriesStartTime: null,
      seriesEndTime: null,
    });
    this.headerText = 'Single occurrence';
    this.timeAfter = null;
    this.timeBefore = new Date();
    this.form.markAsPristine();
    this.form.markAsUntouched();
    this.inboundVoyageSelected = undefined;
    this.outboundVoyageSelected = undefined;
    this.inboundVoyageActive = false;
    this.outboundVoyageActive = false;
    this.inboundVoyageHover.set(false);
    this.outboundVoyageHover.set(false);
    this.inboundVoyageIdValue.set(null);
    this.outboundVoyageIdValue.set(null);
  }

  save() {
    if (this.form.invalid) {
      this.form.markAllAsTouched();
      return;
    }
    const formValue = this.form.value;

    let weeklyPattern = formValue.weeklyPattern;
    if (Array.isArray(weeklyPattern)) {
      weeklyPattern = weeklyPattern.join(',');
    }

    const sailingRequestAssets: SailingRequestAsset[] =
      formValue.sailingRequestAssets!.map((assetId) => {
        return {
          assetId: assetId,
          name: '',
        };
      });

    const activities =
      this.activitiesSelected?.activities.map((activity) => ({
        ...activity,
        sailingRequestActivityId: '00000000-0000-0000-0000-000000000000',
        sailingRequestId: '00000000-0000-0000-0000-000000000000',
        activityCategoryTypeActivityCategoryActivityType: 0,
      })) ?? [];

    let startTime = formValue.startTime
      ? new Date(
          formValue.startTime.getTime() -
            formValue.startTime.getTimezoneOffset() * 60000
        )
          .toISOString()
          .slice(0, 10)
      : null;
    let endTime = formValue.endTime
      ? new Date(
          formValue.endTime.getTime() -
            formValue.endTime.getTimezoneOffset() * 60000
        )
          .toISOString()
          .slice(0, 10)
      : null;

    let seriesStartTime;
    let seriesEndTime;

    if (
      this.form.value.seriesStartTime !== null &&
      this.form.value.seriesEndTime !== null
    ) {
      seriesStartTime = formValue.seriesStartTime
        ? new Date(
            formValue.seriesStartTime.getTime() -
              formValue.seriesStartTime.getTimezoneOffset() * 60000
          )
            .toISOString()
            .slice(0, 10)
        : null;
      seriesEndTime = formValue.seriesEndTime
        ? new Date(
            formValue.seriesEndTime.getTime() -
              formValue.seriesEndTime.getTimezoneOffset() * 60000
          )
            .toISOString()
            .slice(0, 10)
        : null;
    } else {
      seriesStartTime = null;
      seriesEndTime = null;
    }

    const formattedEta = formValue.eta; //this.formatTime(formValue.eta);
    const formattedEtd = formValue.etd; //this.formatTime(formValue.etd);

    const model = {
      ...this.form.value,
      sailingRequestActivities: activities,
      sailingRequestAssets: sailingRequestAssets,
      pilot: this.activitiesSelected?.pilotActive ?? false,
      lightDues: this.activitiesSelected?.lightDuesActive ?? false,
      type: 1,
      eta: this.collectTime(formValue.eta as unknown as Date),
      etd: this.collectTime(formValue.etd as unknown as Date),
      weeklyPattern: weeklyPattern,
      startTime: startTime,
      endTime: endTime,
      seriesStartTime: seriesStartTime,
      seriesEndTime: seriesEndTime,
    } as Partial<SailingRequest> as SailingRequest;

    if (this.editMode) {
      this.store.dispatch(
        LookaheadActions.edit_Sailing_Request({
          sailingRequestId: this.sailingRequestId,
          sailingRequest: model,
        })
      );
    } else {
      this.store.dispatch(
        LookaheadActions.add_Sailing_Request({
          sailingRequest: model,
        })
      );
    }
  }

  deleteSailingRequest(): void {
    this.store.dispatch(
      confirmActions.request_Confirmation({
        titles: {
          title: 'Are you sure you would like to delete this Sailing Request?',
          btnConfirm: 'Yes Delete',
        },
        confirm: LookaheadActions.remove_Sailing_Request({
          id: this.sailingRequestId,
        }),
      })
    );
  }

  cannotAssignVoyage(): boolean {
    return (
      !this.editMode &&
      this.form.value.seriesStartTime !== null &&
      this.form.value.seriesEndTime !== null
    );
  }

  @HostListener('window:beforeunload', ['$event'])
  unloadHandler(event: Event) {
    if (this.form.dirty) {
      const result = confirm('Changes you made may not be saved.');
      if (!result) {
        event.returnValue = false; // Stay on the same page
      }
    }
  }

  collectDate(date: Date) {
    if (!date) {
      return null;
    }
    if (!this.isDate(date)) {
      return date;
    }

    return this.datePipe.transform(date, 'yyyy-MM-dd');
  }

  collectTime(date: Date) {
    if (!date) {
      return null;
    }
    if (!this.isDate(date)) {
      return date;
    }

    return this.datePipe.transform(date, 'HH:mm');
  }

  isDate(value: Date): boolean {
    return value instanceof Date && !isNaN(value.getTime());
  }
}
