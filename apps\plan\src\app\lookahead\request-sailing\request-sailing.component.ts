import {
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  ViewChild,
  inject,
  SimpleChanges,
  OnChanges,
  ChangeDetectorRef,
  HostListener,
  OnDestroy,
  DestroyRef,
  effect,
  ChangeDetectionStrategy,
} from '@angular/core';
import { NgIf, CommonModule } from '@angular/common';
import {
  FormControl,
  ReactiveFormsModule,
  Validators,
  FormsModule,
  FormGroup,
  AbstractControl,
  ValidationErrors,
} from '@angular/forms';
import { Subject, Subscription, startWith } from 'rxjs';
import { map } from 'rxjs/operators';
import { DatePipe } from '@angular/common';
import { MatDialog } from '@angular/material/dialog';
import { Store } from '@ngrx/store';
import { lookaheadFeature } from '../../../../../../libs/services/src/lib/services/lookahead/store/features';
import { assetsFeature } from 'libs/services/src/lib/services/maintenance/store/features';
import { Asset } from 'libs/services/src/lib/services/maintenance/interfaces/asset.interface';
import { RequestSailingRequirementComponent } from './requirement/request-sailing-requirement.component';
import { LookaheadSharedService } from '../../../../../../libs/services/src/lib/services/shared/lookahead-subject.service';
import { ActivityCategory } from '../../../../../../libs/services/src/lib/services/maintenance/interfaces/activity-category.interface';
import { RequestSailingCommentsComponent } from './request-sailing-comments/request-sailing-comments.component';
import { SailingRequest } from '../../../../../../libs/services/src/lib/services/lookahead/interfaces/sailing-request.interface';
import { SailingRequestAsset } from '../../../../../../libs/services/src/lib/services/lookahead/interfaces/sailing-request-asset.interface';
import { Unit } from '../../../../../../libs/services/src/lib/services/interfaces/unit.interface';
import { LookaheadActions } from '../../../../../../libs/services/src/lib/services/lookahead/store/actions/lookahead.action';
import { RequestSailingApprovalComponent } from './approval/request-sailing-approval.component';
import { Vessel } from 'libs/services/src/lib/services/vessels/interfaces/vessel.interface';
import { SailingRequestActivity } from '../../../../../../libs/services/src/lib/services/lookahead/interfaces/sailing-request-activity.interface';
import * as moment from 'moment';
import { User } from 'libs/auth/src/lib/interfaces/user.interface';
import { SailingRequestUserComment } from '../../../../../../libs/services/src/lib/services/lookahead/interfaces/sailing-request-user-comment.interface';
import { ClientLocation } from 'libs/services/src/lib/services/client-locations/interfaces/client-locations.interface';
import { confirmActions } from 'libs/components/src/lib/store/confirm.actions';
import { UtilityService } from '../../../../../../libs/services/src/lib/services/utility.service';
import { planningDetailsFeature } from '../../../../../../libs/services/src/lib/services/voyages/store/features/planning-details.feature';
import { Actions, ofType } from '@ngrx/effects';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { SidebarModule } from 'primeng/sidebar';
import { TabViewModule } from 'primeng/tabview';
import { DropdownModule } from 'primeng/dropdown';
import { MultiSelectModule } from 'primeng/multiselect';
import { CheckboxModule } from 'primeng/checkbox';
import { SpinnerModule } from 'primeng/spinner';
import { ConfirmationService } from 'primeng/api';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { requestPanelFeature } from '../../../../../../libs/services/src/lib/services/lookahead/store/features/request-panel.feature';
import { RequestPanelActions } from '../../../../../../libs/services/src/lib/services/lookahead/store/actions/request-panel.action';
import { stripTimezoneOffset } from 'libs/services/src/lib/services/functions/convert-date.utils';

@Component({
  selector: 'request-sailing',
  templateUrl: './request-sailing.component.html',
  styleUrls: ['./request-sailing.component.scss'],
  standalone: true,
  imports: [
    NgIf,
    ReactiveFormsModule,
    FormsModule,
    RequestSailingRequirementComponent,
    RequestSailingCommentsComponent,
    RequestSailingApprovalComponent,
    CommonModule,
    SidebarModule,
    TabViewModule,
    DropdownModule,
    MultiSelectModule,
    CheckboxModule,
    SpinnerModule,
    ProgressSpinnerModule,
  ],
  providers: [DatePipe],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RequestSailingComponent implements OnInit, OnChanges, OnDestroy {
  // Image paths for inbound button
  readonly inboundNormalImg = 'assets/inbound.svg';
  readonly inboundSelectedImg = 'assets/inbound-selected-active.svg';
  readonly inboundHoverImg = 'assets/inbound-hover.svg';

  // Image paths for outbound button
  readonly outboundNormalImg = 'assets/outbound.svg';
  readonly outboundSelectedImg = 'assets/outbound-selected-active.svg';
  readonly outboundHoverImg = 'assets/outbound-hover.svg';

  // Image paths for interfield button
  readonly interfieldNormalImg = 'assets/interfield.svg';
  readonly interfieldSelectedImg = 'assets/interfield-selected.svg';
  readonly interfieldHoverImg = 'assets/interfield-hover.svg';

  // Getter for the inbound image source
  get inboundImageSrc(): string {
    if (this.form?.controls.isInbound.value) {
      return this.inboundSelectedImg;
    }
    if (this.inboundHover) {
      return this.inboundHoverImg;
    }
    return this.inboundNormalImg;
  }

  // Getter for the outbound image source
  get outboundImageSrc(): string {
    if (this.form?.controls.isOutbound.value) {
      return this.outboundSelectedImg;
    }
    if (this.outboundHover) {
      return this.outboundHoverImg;
    }
    return this.outboundNormalImg;
  }

  // Getter for the interfield image source
  get interfieldImageSrc(): string {
    if (this.form?.controls.isInterfield.value) {
      return this.interfieldSelectedImg;
    }
    if (this.interfieldHover) {
      return this.interfieldHoverImg;
    }
    return this.interfieldNormalImg;
  }

  private readonly confirmationService = inject(ConfirmationService);

  @ViewChild(RequestSailingRequirementComponent)
  requirementComponent!: RequestSailingRequirementComponent;
  @ViewChild(RequestSailingApprovalComponent)
  approvalComponent!: RequestSailingApprovalComponent;
  commentsComponent?: RequestSailingCommentsComponent;
  private readonly actions = inject(Actions);
  private readonly destroyRef = inject(DestroyRef);
  datePipe = inject(DatePipe);
  cdr = inject(ChangeDetectorRef);
  dialog = inject(MatDialog);
  store = inject(Store);
  sharedService = inject(LookaheadSharedService);
  utilityService = inject(UtilityService);
  sailingRequestUserComments$ = this.store.select(
    lookaheadFeature.selectSailingRequestComments
  );
  unsubscribe: Subject<boolean> = new Subject();
  isVisibleRequestPanel = this.store.selectSignal(
    requestPanelFeature.selectIsVisibleRequestPanel
  );

  @ViewChild(RequestSailingCommentsComponent)
  RequestSailingCommentsComponent!: RequestSailingCommentsComponent;
  @Output() isInboundOutboundSidenavOpenedChange = new EventEmitter<{
    isOpen: boolean;
    voyageType: string;
    vesselId: string;
  }>();
  @Output() resetFormChange = new EventEmitter();
  @Input() originalAssets: Asset[] = [];
  @Input() vessels: Vessel[] = [];
  @Input() activityCategories: ActivityCategory[] | undefined;
  @Input() units: Unit[] | undefined;
  @Input() currentUser: User | undefined;
  @Input() clients: ClientLocation[] | undefined;
  assets: Asset[] = [];
  requestSailingId: string = '';
  sailingRequestUserComments: SailingRequestUserComment[] = [];
  headerText = 'Single occurrence';
  searchClusterControl = new FormControl('');
  searchInstallationControl = new FormControl('');
  private subscriptions = new Subscription();
  editMode = false;
  originalClusterAssets: Asset[] = [];
  clusterAssets: Asset[] = [];
  offshoreInstallations = this.store.selectSignal(
    assetsFeature.selectOffshoreAssets
  );
  inboundPlanningDetails = this.store.selectSignal(
    planningDetailsFeature.selectInboundPlanningDetails
  );
  outboundPlanningDetails = this.store.selectSignal(
    planningDetailsFeature.selectOutboundPlanningDetails
  );
  loadingCreateEdit = this.store.selectSignal(
    lookaheadFeature.selectLoadingCreateEdit
  );

  selectedTabIndex: number = 0;
  showErrors: boolean = false;
  showApprovalErrors: boolean = false;

  // Properties for tracking transport request completion status
  hasCompletedRequests: boolean = false;
  isInboundRequestComplete: boolean = false;
  isOutboundRequestComplete: boolean = false;
  isInterfieldRequestComplete: boolean = false;

  form = new FormGroup(
    {
      clusterId: new FormControl<string | null>(null),
      sailingRequestAssets: new FormControl<string[]>(
        [],
        [Validators.required]
      ),
      isInbound: new FormControl<boolean>(false, [Validators.required]),
      isOutbound: new FormControl<boolean>(false, [Validators.required]),
      isInterfield: new FormControl<boolean>(false, [Validators.required]),
      vesselId: new FormControl<string | null>(null),
      clientId: new FormControl<string | null>(null),
      inboundVoyageId: new FormControl<string | null>(null),
      outboundVoyageId: new FormControl<string | null>(null),
      startTime: new FormControl<Date | null>(null),
      endTime: new FormControl<Date | null>(null),
      eta: new FormControl<string | Date>(''),
      etd: new FormControl<string | Date>(''),
      status: new FormControl<number>(0),
      comment: new FormControl<string>(''),
      isFlexableTiming: new FormControl<boolean>(false),
      arrivalTime: new FormControl<Date | null>(null),
      firstInstallationTime: new FormControl<Date | null>(null),
      latestArrivalTime: new FormControl<Date | null>(null),
      clusterTime: new FormControl<number | null>(null),
      timeUnit: new FormControl<string>(''),
      isMailbag: new FormControl<boolean>(false),
      isBulkReq: new FormControl<boolean>(false),
      clientReference: new FormControl<string>(''),
      remarks: new FormControl<string>(''),
      sailingRequestActivities: new FormControl<SailingRequestActivity[]>([]),
      seriesStartTime: new FormControl<Date | null>(null),
      seriesEndTime: new FormControl<Date | null>(null),
      weeklyPattern: new FormControl<string>(''),
      repeatEveryNumberOfWeeks: new FormControl<number | null>(null),
      selectAll: new FormControl<boolean>(false),
      autoFillCluster: new FormControl(true),
    },
    { validators: this.atLeastOneToggleValidator }
  );
  selectedDate: Date = new Date();
  interfieldHover: boolean = false;
  inboundHover: boolean = false;
  outboundHover: boolean = false;
  isFormDirty = false;
  approvalData: [] = [];
  addUserToComments = false;
  nextButtonClicked = false;
  viewTransportRequests = false;
  isPartOfCluster = true;
  originalOffshoreInstallations: Asset[] = [];
  filteredOffshoreInstallations: Asset[] = [];
  filteredActivities: ActivityCategory[] = [];

  panelSailingRequest = this.store.selectSignal(
    requestPanelFeature.selectPanelSailingRequest
  );

  constructor() {
    effect(
      () => {
        const request = this.panelSailingRequest();
        if (request) {
          this.editMode = true;
          this.requestSailingId = request.Id;
          this.patchForm(request);
        } else {
          this.editMode = false;
          this.resetForm();
        }
      },
      { allowSignalWrites: true }
    );
  }

  ngOnInit() {
    this.filteredOffshoreInstallations = this.offshoreInstallations();

    this.subToSailingRequestUserComments();
    this.filters();
    this.subscriptions.add(
      this.sharedService.editPoolSailingRequestSubject.subscribe((data) => {})
    );
    this.subscriptions.add(
      this.sharedService.resetRequestSailingFormSubject.subscribe(() => {
        this.resetRequestSailingForm();
      })
    );
    this.form.patchValue({ startTime: new Date() });

    this.filters();
    this.subscribeToControlChanges('isInbound');
    this.subscribeToControlChanges('isOutbound');
    this.subscribeToControlChanges('isInterfield');
    this.subscribeToControlChanges('isInterfield');
  }

  closeSideBar(visible: boolean) {
    if (!visible) {
      if (
        this.form.dirty ||
        (this.editMode &&
          this.RequestSailingCommentsComponent?.IsCommentsFormDirty())
      ) {
        this.openConfirmationDialog();
      } else {
        this.sharedService.resetRequestSailingForm();
        this.sharedService.closeInboundOutboundSidenav();
        this.selectedTabIndex = 0;
        this.nextButtonClicked = false;
        this.store.dispatch(
          RequestPanelActions.change_Visibility_Request_Panel({ visible })
        );
        if (this.editMode) {
          this.RequestSailingCommentsComponent?.ResetForm();
        }
      }
    }
  }

  ngOnChanges(changes: SimpleChanges) {
    this.originalClusterAssets = this.utilityService.getClusterAssets(
      this.originalAssets
    );
    if (
      changes['originalAssets'] &&
      !changes['originalAssets'].isFirstChange()
    ) {
      this.assets = Object.assign([], this.originalAssets);
      this.filters();
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  private patchForm(sailingRequest: any): void {
    this.isPartOfCluster = true;
    const sailingRequestAssets = sailingRequest.SailingRequestAssets ?? [];
    this.form.patchValue({
      clusterId: sailingRequest.ClusterId,
      isInbound: sailingRequest.IsInbound,
      isOutbound: sailingRequest.IsOutbound,
      isInterfield: sailingRequest.IsInterfield,
      clientId: sailingRequest.ClientId,
      vesselId: sailingRequest.VesselId,
      sailingRequestAssets: sailingRequestAssets.map(
        (asset: any) => asset.assetId
      ),
      startTime: sailingRequest.StartTime
        ? new Date(sailingRequest.StartTime)
        : new Date(),
      endTime: sailingRequest.EndTime ? new Date(sailingRequest.EndTime) : null,
      eta: sailingRequest.Eta,
      etd: sailingRequest.Etd,
      inboundVoyageId: sailingRequest.InboundVoyageId,
      outboundVoyageId: sailingRequest.OutboundVoyageId,
      weeklyPattern: sailingRequest.WeeklyPattern,
      repeatEveryNumberOfWeeks: sailingRequest.RepeatEveryNumberOfWeeks,
      sailingRequestActivities: sailingRequest.SailingRequestActivities,
      status: sailingRequest.Status,
      comment: sailingRequest.Comment,
      isFlexableTiming: sailingRequest.IsFlexableTiming,
      arrivalTime: sailingRequest.ArrivalTime
        ? sailingRequest.ArrivalTime
        : null,
      firstInstallationTime: sailingRequest.FirstInstallationTime
        ? sailingRequest.FirstInstallationTime
        : null,
      latestArrivalTime: sailingRequest.LatestArrivalTime
        ? sailingRequest.LatestArrivalTime
        : null,
      clusterTime: sailingRequest.ClusterTime,
      timeUnit: sailingRequest.TimeUnit,
      isMailbag: sailingRequest.IsMailbag,
      isBulkReq: sailingRequest.IsBulkReq,
      clientReference: sailingRequest.ClientReference,
      remarks: sailingRequest.Remarks,
    });
    if (
      sailingRequest.SeriesStartTime !== null &&
      sailingRequest.SeriesEndTime !== null
    ) {
      this.headerText = 'Series';
      this.form.controls.seriesStartTime.setValue(
        sailingRequest.SeriesStartTime
      );
      this.form.controls.seriesEndTime.setValue(sailingRequest.SeriesEndTime);
    }

    if (sailingRequest.ClusterId) {
      this.onClusterChange(sailingRequest.ClusterId, true);
    } else if (sailingRequestAssets.length > 0) {
      this.checkIfAssetBelongsToCluster();
    }

    this.inboundHover = sailingRequest.IsInbound;
    this.outboundHover = sailingRequest.IsOutbound;
    this.interfieldHover = sailingRequest.IsInterfield;

    // Check transport request completion status
    if (
      sailingRequest.TransportRequests &&
      sailingRequest.TransportRequests.length > 0
    ) {
      const transportRequests = sailingRequest.TransportRequests;
      const completedRequests = transportRequests.filter(
        (tr: any) => tr.isComplete
      ).length;
      this.hasCompletedRequests = completedRequests > 0;
      // Check inbound request status
      const inboundRequests = transportRequests.filter(
        (tr: any) => tr.voyageDirection === 0
      );
      this.isInboundRequestComplete =
        inboundRequests.length > 0 &&
        inboundRequests.every((tr: any) => tr.isComplete);
      // Check outbound request status
      const outboundRequests = transportRequests.filter(
        (tr: any) => tr.voyageDirection === 1
      );
      this.isOutboundRequestComplete =
        outboundRequests.length > 0 &&
        outboundRequests.every((tr: any) => tr.isComplete);
      // Check interfield request status
      const interfieldRequests = transportRequests.filter(
        (tr: any) => tr.voyageDirection === 2
      );
      this.isInterfieldRequestComplete =
        interfieldRequests.length > 0 &&
        interfieldRequests.every((tr: any) => tr.isComplete);
    } else {
      this.hasCompletedRequests = false;
      this.isInboundRequestComplete = false;
      this.isOutboundRequestComplete = false;
      this.isInterfieldRequestComplete = false;
    }
    this.updateFormControlsDisabledState();
  }

  private subToSailingRequestUserComments(): void {
    this.sailingRequestUserComments$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((res) => {
        this.sailingRequestUserComments = res;
        this.showNotificationOnComments();
      });
  }

  private subscribeToControlChanges(controlName: string) {
    const control = this.form.get(controlName);
    if (control) {
      control.valueChanges.subscribe(() => {
        control.markAsTouched(); // Manually mark the control as touched
        this.checkFormDirty();
      });
      control.statusChanges.subscribe(() => {
        this.checkFormDirty();
      });
    }
  }

  @HostListener('window:beforeunload', ['$event'])
  unloadHandler(event: Event) {
    if (this.form.dirty) {
      const result = confirm('Changes you made may not be saved.');
      if (!result) {
        event.returnValue = false;
      }
    }
  }

  handleDataFromChild(event: {
    formData: any;
    sailingRequestActivities: any[];
  }) {
    const { formData, sailingRequestActivities } = event;
    this.patchRequirementFormData(formData, sailingRequestActivities);
  }

  approvalDataChanged(approvalData: SailingRequest) {
    this.patchApprovalFormData(approvalData);
  }

  commentsDataChanged(event: any) {}

  patchRequirementFormData(formData: any, sailingRequestActivities: any[]) {
    this.form.patchValue({
      clientId: formData.clientId,
      isFlexableTiming: formData.isFlexableTiming,
      arrivalTime: this.convertMomentToDate(formData.arrivalTime),
      firstInstallationTime: formData.firstInstallationTime,
      latestArrivalTime: formData.latestArrivalTime,
      clusterTime: formData.clusterTime,
      timeUnit: formData.timeUnit,
      isMailbag: formData.isMailbag,
      isBulkReq: formData.isBulkReq,
      clientReference: formData.clientReference,
      remarks: formData.remarks,
      endTime: formData.endTime ? new Date(formData.endTime) : null,
      weeklyPattern: formData.weeklyPattern,
      seriesStartTime: formData.seriesStartTime
        ? formData.seriesStartTime
        : null,
      seriesEndTime: formData.seriesEndTime ? formData.seriesEndTime : null,
      repeatEveryNumberOfWeeks: formData.repeatEveryNumberOfWeeks,
      sailingRequestActivities: sailingRequestActivities,
    });
  }

  patchApprovalFormData(approvalData: any) {
    this.form.patchValue({
      vesselId: approvalData.vesselId,
      inboundVoyageId: approvalData.inboundVoyageId,
      outboundVoyageId: approvalData.outboundVoyageId,
      comment: approvalData.comment,
      startTime: approvalData.startTime ? approvalData.startTime : null,
      endTime: approvalData.endTime ? approvalData.endTime : null,
      weeklyPattern: approvalData.weeklyPattern,
      seriesStartTime: approvalData.seriesStartTime
        ? approvalData.seriesStartTime
        : null,
      seriesEndTime: approvalData.seriesEndTime
        ? approvalData.seriesEndTime
        : null,
      repeatEveryNumberOfWeeks: approvalData.repeatEveryNumberOfWeeks,
      eta: approvalData.eta,
      etd: approvalData.etd,
      status: approvalData.status,
    });
  }

  showNotificationOnComments() {
    if (this.sailingRequestUserComments.length) {
      const lastComment =
        this.sailingRequestUserComments?.[
          this.sailingRequestUserComments.length - 1
        ];
      const isLastCommentByCurrentUser =
        lastComment.createdByUserId === this.currentUser?.userId;
      if (isLastCommentByCurrentUser) {
        return;
      }
      const hasUserReadAllComments = lastComment?.commentReadByUsers?.some(
        (x: any) => x.readerUserId === this.currentUser?.userId
      );
      if (hasUserReadAllComments) {
        return;
      }
      this.addUserToComments = true;
    }
  }
  onTabChange(event: any) {
    if (this.selectedTabIndex === 3) {
      this.selectedTabIndex = 3;
      if (this.editMode) {
        this.RequestSailingCommentsComponent.EnableAutoScroll();
        this.RequestSailingCommentsComponent.ScrollToBottom();
        if (this.addUserToComments) {
          this.RequestSailingCommentsComponent.AddUserToCommentsReadBy();
        }
        this.addUserToComments = false;
      }
    }
  }

  allowChildToSendComment() {
    this.editMode && this.RequestSailingCommentsComponent.AddComment();
  }

  onClusterChange(selectedClusterId: string, isFromPatch = false) {
    this.searchInstallationControl.setValue('');

    if (!isFromPatch) {
      this.form.get('sailingRequestAssets')?.setValue([]);
    }

    if (!this.originalOffshoreInstallations.length) {
      this.originalOffshoreInstallations = this.offshoreInstallations();
    }

    if (selectedClusterId) {
      // filter by head, member, or explicit children
      const cluster = this.originalClusterAssets.find(
        (c) => c.assetId === selectedClusterId
      );
      this.filteredOffshoreInstallations =
        this.originalOffshoreInstallations.filter(
          (asset) =>
            asset.assetId === selectedClusterId ||
            asset.clusterHeadId === selectedClusterId ||
            cluster?.clusterChildren?.includes(asset.assetId)
        );
    } else {
      // restore full list when no cluster selected
      this.filteredOffshoreInstallations = this.originalOffshoreInstallations;
    }

    this.cdr.detectChanges();
  }

  private filters(): void {
    this.searchClusterControl.valueChanges
      .pipe(
        startWith(''),
        map((value) => this._filterCluster(value!))
      )
      .subscribe((filteredAssets) => {
        this.clusterAssets = filteredAssets;
      });

    this.searchInstallationControl.valueChanges
      .pipe(
        startWith(''),
        map((searchValue) => this._filterInstallation(searchValue!))
      )
      .subscribe((filteredAssets) => {
        this.filteredOffshoreInstallations = filteredAssets;
      });

    if (
      !this.filteredOffshoreInstallations?.length &&
      this.offshoreInstallations()?.length
    ) {
      this.originalOffshoreInstallations = this.offshoreInstallations();
      this.filteredOffshoreInstallations = this.originalOffshoreInstallations;
    }

    this.cdr.detectChanges();
  }

  private _filterCluster(value: string | null): Asset[] {
    if (value === '') {
      return this.originalClusterAssets ?? [];
    }

    const filterValue = value!.toLowerCase();
    const numericFilterValue = value!.toString().toLowerCase();

    return this.originalClusterAssets!.filter(
      (asset) =>
        asset.name.toLowerCase().includes(filterValue) ||
        asset.name.toLowerCase().includes(numericFilterValue)
    );
  }

  private _filterInstallation(value: string | null): Asset[] {
    if (!this.originalOffshoreInstallations || !this.offshoreInstallations()) {
      return [];
    }

    if (value === '') {
      return this.originalOffshoreInstallations ?? [];
    }

    const filterValue = value!.toLowerCase();
    const numericFilterValue = value!.toString().toLowerCase();

    return this.originalOffshoreInstallations.filter(
      (asset) =>
        asset.name.toLowerCase().includes(filterValue) ||
        asset.name.toLowerCase().includes(numericFilterValue)
    );
  }

  clearClusterSelection() {
    this.form.get('clusterId')?.setValue(null);
    this.searchClusterControl.setValue('');
    this.form.get('sailingRequestAssets')?.setValue([]);
    this.searchInstallationControl.setValue('');

    this.filteredOffshoreInstallations =
      this.originalOffshoreInstallations || this.offshoreInstallations();

    this.cdr.detectChanges();
  }

  private updateFormControlsDisabledState(): void {
    if (this.hasCompletedRequests) {
      this.form.get('clusterId')?.disable({ emitEvent: false });
      this.form.get('sailingRequestAssets')?.disable({ emitEvent: false });
      this.form.get('selectAll')?.disable({ emitEvent: false });
    } else {
      this.form.get('clusterId')?.enable({ emitEvent: false });
      this.form.get('sailingRequestAssets')?.enable({ emitEvent: false });
      this.form.get('selectAll')?.enable({ emitEvent: false });
    }

    // Handle voyage direction buttons
    if (this.isInboundRequestComplete) {
      this.form.get('isInbound')?.disable({ emitEvent: false });
    } else {
      this.form.get('isInbound')?.enable({ emitEvent: false });
    }
    if (this.isOutboundRequestComplete) {
      this.form.get('isOutbound')?.disable({ emitEvent: false });
    } else {
      this.form.get('isOutbound')?.enable({ emitEvent: false });
    }
    if (this.isInterfieldRequestComplete) {
      this.form.get('isInterfield')?.disable({ emitEvent: false });
    } else {
      this.form.get('isInterfield')?.enable({ emitEvent: false });
    }
  }

  // There is a copy of this in the Request App's request-sailing.component.ts/html
  // This is to prevent the two apps possible future requirements from conflicting
  checkIfAssetBelongsToCluster() {
    this.form.get('clusterId')?.enable();
    // get selected installations
    const selected = this.form.get('sailingRequestAssets')?.value || [];
    if (!selected.length) {
      this.isPartOfCluster = true;
      this.filteredOffshoreInstallations = this.originalOffshoreInstallations
        ?.length
        ? this.originalOffshoreInstallations
        : this.offshoreInstallations();
      return;
    }

    const installations = this.offshoreInstallations();
    // preserve original list
    if (!this.originalOffshoreInstallations.length) {
      this.originalOffshoreInstallations = installations;
    }

    const installation = installations.find(
      (inst) => inst.assetId === selected[0]
    );
    if (!installation) return;

    // determine cluster head ID if any
    let headId: string | null = null;
    const isClusterHead = this.originalClusterAssets.some(
      (c) => c.assetId === installation.assetId
    );
    const isClusterMember = Boolean(
      installation.clusterHeadId &&
        this.originalClusterAssets.some(
          (c) => c.assetId === installation.clusterHeadId
        )
    );

    if (isClusterHead) headId = installation.assetId;
    else if (isClusterMember) headId = installation.clusterHeadId!;

    if (headId) {
      // update form and filter by cluster membership
      this.isPartOfCluster = true;
      this.form.get('clusterId')?.setValue(headId);
      const cluster = this.originalClusterAssets.find(
        (c) => c.assetId === headId
      )!;
      this.filteredOffshoreInstallations =
        this.originalOffshoreInstallations.filter(
          (asset) =>
            asset.assetId === headId ||
            asset.clusterHeadId === headId ||
            cluster.clusterChildren?.includes(asset.assetId)
        );
      this.onClusterChange(headId, true);
    } else {
      // no cluster: disable dropdown and show only standalone assets
      this.form.get('clusterId')?.disable();
      this.isPartOfCluster = false;
      this.form.get('clusterId')?.setValue(null);
      this.filteredOffshoreInstallations =
        this.originalOffshoreInstallations.filter(
          (asset) =>
            !asset.clusterHeadId &&
            !this.originalClusterAssets.some((c) => c.assetId === asset.assetId)
        );
    }

    this.cdr.detectChanges();
  }

  onAutoFillClusterChange(event: any) {
    // If we have installations selected, try to determine the cluster
    const selectedAssets = this.form.get('sailingRequestAssets')?.value;
    if (selectedAssets && selectedAssets.length > 0) {
      this.autoFillClusterFromInstallations();
    }
  }

  autoFillClusterFromInstallations() {
    const selectedInstallations = this.form.get('sailingRequestAssets')?.value;
    if (!selectedInstallations || selectedInstallations.length === 0) return;

    const firstInstallationId = selectedInstallations[0];
    const installation = this.offshoreInstallations().find(
      (inst: Asset) => inst.assetId === firstInstallationId
    );

    if (installation && installation.clusterHeadId) {
      // Set the cluster ID to the installation's cluster head ID
      this.form.get('clusterId')?.setValue(installation.clusterHeadId);
      if (installation.clusterHeadId) {
        this.onClusterChange(installation.clusterHeadId, true);
      } else if (selectedInstallations.length > 0) {
        this.checkIfAssetBelongsToCluster();
      }
    }
  }

  clearInstallationSelection() {
    this.form.get('clusterId')?.enable();
    this.form.get('sailingRequestAssets')?.setValue([]);
    this.searchInstallationControl.setValue('');
    this.isPartOfCluster = true;
  }

  onInstallationSelectionChange() {
    this.form.get('clusterId')?.enable();
    const selectedInstallations = this.form.get('sailingRequestAssets')?.value;

    // If no installations are selected, clear the cluster dropdown
    if (!selectedInstallations || selectedInstallations.length === 0) {
      this.form.get('clusterId')?.setValue(null);
      this.isPartOfCluster = true;
      this.filteredOffshoreInstallations =
        this.originalOffshoreInstallations || this.offshoreInstallations();
      return;
    }

    this.checkIfAssetBelongsToCluster();
  }

  convertMomentToDate(date: any): Date | null {
    if (moment.isMoment(date)) {
      return date.toDate();
    }
    return date;
  }

  openConfirmationDialog() {
    this.confirmationService.confirm({
      message:
        'You have unsaved changes. Are you sure you want to proceed without saving?',
      header: 'Unsaved Changes',
      rejectButtonStyleClass: 'btn-tertiary',
      acceptButtonStyleClass: 'btn-negative-primary',
      acceptIcon: 'none',
      rejectIcon: 'none',
      acceptLabel: 'Proceed',
      rejectLabel: 'Cancel',
      accept: () => {
        this.sharedService.resetRequestSailingForm();
        this.sharedService.closeInboundOutboundSidenav();
        if (this.editMode && this.commentsComponent) {
          this.commentsComponent.ResetForm();
        }
        this.selectedTabIndex = 0;
        this.nextButtonClicked = false;
        this.store.dispatch(
          RequestPanelActions.change_Visibility_Request_Panel({
            visible: false,
          })
        );
      },
    });
  }

  resetRequestSailingForm() {
    this.resetForm();
    this.selectedTabIndex = 0;
    this.updateFormControlsDisabledState();
  }

  outboundNotSelected(): boolean {
    return (
      this.form.get('isInterfield')?.value! &&
      !this.form.get('isOutbound')?.value
    );
  }

  voyageTypeNotSelected(): boolean {
    return (
      !this.form.get('isInbound')?.value &&
      !this.form.get('isOutbound')?.value &&
      !this.form.get('isInterfield')?.value
    );
  }

  isRequirementDisabled(): boolean {
    const isVoyageDirectionNotSelected =
      !this.form.get('isInbound')?.value &&
      !this.form.get('isOutbound')?.value &&
      !this.form.get('isInterfield')?.value;

    const installationsNotSelected = !this.form.get('sailingRequestAssets')
      ?.value?.length;

    if (isVoyageDirectionNotSelected || installationsNotSelected) return true;
    else return false;
  }

  isApprovalsDisabled(): boolean {
    if (this.requirementComponent?.form?.invalid) {
      return true;
    } else return false;
  }

  goToNextTab() {
    this.form.markAsTouched();
    this.showErrors = true;
    this.nextButtonClicked = true;
    switch (this.selectedTabIndex) {
      case 0: {
        if (
          !this.voyageTypeNotSelected() &&
          this.form.get('sailingRequestAssets')?.value?.length
        ) {
          this.selectedTabIndex += 1;
          this.showErrors = false;
        }
        break;
      }
      case 1: {
        if (!this.requirementComponent.form.invalid) {
          this.selectedTabIndex += 1;
          this.showErrors = false;
        }
        break;
      }
      case 2: {
        if (!this.approvalComponent.form.invalid) {
          this.selectedTabIndex += 1;
          this.showErrors = false;
        }
        break;
      }
    }
  }

  hasOnlyCliRoleInPlan(): boolean {
    const planRole = this.currentUser?.roles.find(
      (role: any) => role.application === 'plan'
    );
    return planRole
      ? planRole.roles.length === 1 && planRole.roles.includes('CLI')
      : false;
  }

  save() {
    this.showApprovalErrors = true;

    const etaValidationFailed =
      !this.hasOnlyCliRoleInPlan() &&
      this.approvalComponent.isEtaRequired() &&
      (!this.approvalComponent.form.get('eta')?.value ||
        this.approvalComponent.form.get('eta')?.value === '');

    const etdValidationFailed =
      !this.hasOnlyCliRoleInPlan() &&
      this.approvalComponent.isEtdRequired() &&
      (!this.approvalComponent.form.get('etd')?.value ||
        this.approvalComponent.form.get('etd')?.value === '');

    if (
      this.form.invalid ||
      this.requirementComponent.form.invalid ||
      (!this.hasOnlyCliRoleInPlan() && this.approvalComponent.form.invalid) ||
      etaValidationFailed ||
      etdValidationFailed
    ) {
      this.requirementComponent.form.markAllAsTouched();
      this.approvalComponent.form.markAllAsTouched();
      return;
    }

    const formValue = this.form.getRawValue();

    let weeklyPattern = formValue.weeklyPattern;
    if (Array.isArray(weeklyPattern)) {
      weeklyPattern = weeklyPattern.join(',');
    }

    const sailingRequestAssets: SailingRequestAsset[] =
      formValue.sailingRequestAssets?.map((assetId) => {
        return {
          assetId: assetId,
          name: '',
        };
      }) || [];

    const clientId = this.hasOnlyCliRoleInPlan()
      ? this.currentUser?.clientId
      : formValue.clientId;

    const activities =
      formValue.sailingRequestActivities?.map((activity) => ({
        ...activity,
        sailingRequestActivityId: '00000000-0000-0000-0000-000000000000',
        sailingRequestId: '00000000-0000-0000-0000-000000000000',
        activityCategoryTypeActivityCategoryActivityType: 0,
      })) ?? [];

    const arrivalTime = formValue.arrivalTime;
    const firstInstallationTime = formValue.firstInstallationTime;
    const latestArrivalTime = formValue.latestArrivalTime;

    const model = {
      ...formValue,
      arrivalTime: arrivalTime ? stripTimezoneOffset(arrivalTime) : null,
      firstInstallationTime: firstInstallationTime
        ? stripTimezoneOffset(firstInstallationTime)
        : null,
      latestArrivalTime: latestArrivalTime
        ? stripTimezoneOffset(latestArrivalTime)
        : null,
      clientId,
      startTime: this.collectDate(formValue.startTime!),
      endTime: this.collectDate(formValue.endTime!),
      eta: this.collectTime(formValue.eta as unknown as Date),
      etd: this.collectTime(formValue.etd as unknown as Date),
      sailingRequestAssets,
      type: 0,
      weeklyPattern: weeklyPattern,
      sailingRequestActivities: activities,
    } as Partial<SailingRequest> as SailingRequest;

    model.firstInstallationTime = model.isFlexableTiming
      ? null
      : model.firstInstallationTime;
    model.latestArrivalTime = model.isFlexableTiming
      ? model.latestArrivalTime
      : null;

    if (this.editMode) {
      this.store.dispatch(
        LookaheadActions.edit_Sailing_Request({
          sailingRequestId: this.requestSailingId,
          sailingRequest: model,
        })
      );
    } else {
      this.store.dispatch(
        LookaheadActions.add_Sailing_Request({ sailingRequest: model })
      );
    }

    this.actions
      .pipe(
        ofType(
          LookaheadActions.edit_Sailing_Request_Success,
          LookaheadActions.add_Sailing_Request_Success
        ),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe((action) => {
        this.store.dispatch(
          RequestPanelActions.change_Visibility_Request_Panel({
            visible: false,
          })
        );
      });
  }

  collectDate(date: Date) {
    if (!date) {
      return null;
    }
    if (!this.isDate(date)) {
      return date;
    }

    return this.datePipe.transform(date, 'yyyy-MM-dd');
  }

  collectTime(date: Date) {
    if (!date) {
      return null;
    }
    if (!this.isDate(date)) {
      return date;
    }

    return this.datePipe.transform(date, 'HH:mm');
  }

  isDate(value: Date): boolean {
    return value instanceof Date && !isNaN(value.getTime());
  }

  deleteSailingRequest(): void {
    this.store.dispatch(
      confirmActions.request_Confirmation({
        titles: {
          title: 'Are you sure you would like to delete this Sailing Request?',
          btnConfirm: 'Yes Delete',
        },
        confirm: LookaheadActions.remove_Sailing_Request({
          id: this.requestSailingId,
        }),
      })
    );
  }

  resetForm() {
    const resetValues = {
      clusterId: '',
      sailingRequestAssets: [],
      isInbound: false,
      isOutbound: false,
      isInterfield: false,
      vesselId: null,
      clientId: null,
      inboundVoyageId: null,
      outboundVoyageId: null,
      startTime: null,
      endTime: null,
      eta: '',
      etd: '',
      status: 0,
      comment: '',
      isFlexableTiming: false,
      arrivalTime: null,
      firstInstallationTime: null,
      latestArrivalTime: null,
      clusterTime: null,
      timeUnit: '',
      isMailbag: false,
      isBulkReq: false,
      clientReference: '',
      remarks: '',
      sailingRequestActivities: [],
      seriesStartTime: null,
      seriesEndTime: null,
      weeklyPattern: '',
      repeatEveryNumberOfWeeks: null,
      selectAll: false,
      autoFillCluster: false,
    };

    this.form.reset(resetValues);
    this.form.markAsPristine();
    this.form.markAsUntouched();
    this.resetFormChange.emit();
    this.nextButtonClicked = false;
    this.inboundHover = false;
    this.outboundHover = false;
    this.interfieldHover = false;
    this.hasCompletedRequests = false;
    this.isInboundRequestComplete = false;
    this.isOutboundRequestComplete = false;
    this.isInterfieldRequestComplete = false;

    this.form.get('clusterId')?.enable({ emitEvent: false });
    this.form.get('sailingRequestAssets')?.enable({ emitEvent: false });
    this.form.get('isInbound')?.enable({ emitEvent: false });
    this.form.get('isOutbound')?.enable({ emitEvent: false });
    this.form.get('isInterfield')?.enable({ emitEvent: false });
    this.form.get('selectAll')?.enable({ emitEvent: false });

    // Reset filtered installations to original list
    this.isPartOfCluster = true;
    this.filteredOffshoreInstallations = this.originalOffshoreInstallations
      ?.length
      ? this.originalOffshoreInstallations
      : this.offshoreInstallations();
    this.searchInstallationControl.setValue('');

    setTimeout(() => {
      this.editMode = false;
    }, 500);
  }

  inboundSelect() {
    this.form.controls.isInbound.setValue(!this.form.controls.isInbound.value);
  }

  outboundSelect() {
    this.form.controls.isOutbound.setValue(
      !this.form.controls.isOutbound.value
    );
  }

  interfieldSelect() {
    this.form.controls.isInterfield.setValue(
      !this.form.controls.isInterfield.value
    );
  }

  selectAll(event: boolean) {
    let isChecked = event;
    this.form.patchValue({
      isInbound: isChecked,
      isOutbound: isChecked,
      isInterfield: isChecked,
    });
  }

  atLeastOneToggleValidator(control: AbstractControl): ValidationErrors | null {
    const inboundToggle = control.get('isInbound')?.value;
    const outboundToggle = control.get('isOutbound')?.value;
    const interfieldToggle = control.get('isInterfield')?.value;

    if (interfieldToggle && !outboundToggle) {
      return { noToggleSelected: false };
    }

    return inboundToggle || outboundToggle ? null : { noToggleSelected: true };
  }

  private checkFormDirty() {
    const inboundControl = this.form.get('isInbound');
    const outboundControl = this.form.get('isOutbound');
    const interfieldControl = this.form.get('isInterfield');

    const inboundTouched = inboundControl?.touched ?? false;
    const outboundTouched = outboundControl?.touched ?? false;
    const interfieldTouched = interfieldControl?.touched ?? false;

    const inboundValue = inboundControl?.value ?? false;
    const outboundValue = outboundControl?.value ?? false;
    const interfieldValue = interfieldControl?.value ?? false;

    const anyToggleSelected = inboundValue || outboundValue || interfieldValue;
    const anyToggleTouched =
      inboundTouched || outboundTouched || interfieldTouched;

    if (inboundValue && !outboundValue && !interfieldValue) {
      this.filteredActivities = this.filterActivitiesForInbound();
    } else if (!inboundValue && (outboundValue || interfieldValue)) {
      this.filteredActivities = this.activityCategories!;
    } else {
      this.filteredActivities = this.activityCategories!;
    }

    this.isFormDirty = anyToggleTouched && !anyToggleSelected;
  }

  private filterActivitiesForInbound(): any[] {
    return this.activityCategories!.map((category) => ({
      ...category,
      activityCategoryTypes: category.activityCategoryTypes.filter(
        (type) => !type.createOutOfPortActivity
      ),
    })).filter((category) => category.activityCategoryTypes.length > 0);
  }
}
