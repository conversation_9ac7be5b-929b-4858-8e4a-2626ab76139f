<p-sidebar
  [visible]="isVisibleRequestPanel()"
  (visibleChange)="closeSideBar($event)"
  position="right"
  [style]="{ width: '400px', backgroundColor: 'white' }"
>
  <ng-template pTemplate="header">
    <div class="fs-20 f-bold">{{ editMode ? 'Edit' : 'Add' }} Request</div>
  </ng-template>

  <div class="fs-14 fw-400">
    <div class="mb-12">
      <p>Complete the below section for your request sailing.</p>
    </div>
    <p-tabView
      styleClass="border-bottom pb-22"
      [(activeIndex)]="selectedTabIndex"
      (activeIndexChange)="onTabChange($event)"
    >
      <p-tabPanel header="Vessel info" class="mb-24">
        <div class="w-100">
          <form [formGroup]="form" class="d-flex gap-16">
            <div class="flex-1 d-flex flex-direction-column gap-8">
              <div class="fs-14 d-flex align-items-center gap-8 mt-12">
                <img
                  src="assets/arrow.svg"
                  alt="users-plus"
                  style="margin-right: 4px; height: 20px; width: 20px"
                />
                <b>{{ headerText }}</b>
              </div>

              <div class="d-flex flex-direction-column gap-4 mt-12">
                <p class="fs-14 d-flex flex-direction-column gap-4">
                  Select cluster to create a request.
                </p>
                <div class="d-flex flex-direction-column gap-4">
                  <p-dropdown
                    [options]="clusterAssets"
                    formControlName="clusterId"
                    optionValue="assetId"
                    optionLabel="name"
                    [showClear]="true"
                    [filter]="true"
                    [placeholder]="'Add Cluster'"
                    appendTo="body"
                    (onChange)="onClusterChange($event.value)"
                    (onClear)="clearClusterSelection()"
                    styleClass="new-version"
                    panelStyleClass="new-version-panel"
                  >
                  </p-dropdown>
                </div>
              </div>

              <div class="d-flex flex-direction-column gap-4 mt-12">
                <div class="d-flex align-content-center">
                  <b>
                    Offshore Installations <span style="color: red">*</span>
                  </b>
                </div>
                <p class="fs-14 d-flex flex-direction-column gap-4">
                  Only select the offshore installations to be visited
                </p>
                <div class="mb-24">
                  <p-multiSelect
                    [options]="filteredOffshoreInstallations"
                    formControlName="sailingRequestAssets"
                    optionValue="assetId"
                    optionLabel="name"
                    [showClear]="true"
                    [filter]="true"
                    [placeholder]="'Add Offshore Installations'"
                    appendTo="body"
                    (onChange)="onInstallationSelectionChange()"
                    (onClear)="clearInstallationSelection()"
                    styleClass="new-version-multiselect"
                    panelStyleClass="new-version-panel"
                  />
                  <small
                    class="validation-control-error"
                    *ngIf="
                      form.get('sailingRequestAssets')?.invalid &&
                      form.get('sailingRequestAssets')?.touched
                    "
                  >
                    Offshore installations are required
                  </small>
                </div>
              </div>

              <div class="d-flex flex-direction-column gap-4 mt-12">
                <div class="d-flex align-content-center">
                  <b>Types <span style="color: red">*</span></b>
                </div>
                <p class="fs-14 d-flex flex-direction-column gap-4">
                  Select a direction. Transport request requires a selection
                </p>
                <div class="d-flex mb-16">
                  <p-checkbox
                    formControlName="selectAll"
                    [binary]="true"
                    (onChange)="selectAll($event.checked)"
                    [label]="'Select all'"
                  ></p-checkbox>
                </div>
                <div class="d-flex">
                  <div class="col ml-8">
                    <button
                      class="toggle-button"
                      style="margin-left: -8px"
                      (click)="inboundSelect()"
                      (mouseenter)="inboundHover = true"
                      (mouseleave)="inboundHover = false"
                      [class.disabled]="isInboundRequestComplete"
                      [disabled]="form.get('isInbound')?.disabled"
                    >
                      <img
                        class="select-image"
                        [src]="inboundImageSrc"
                        alt="Inbound"
                      />
                    </button>
                  </div>
                  <div class="col ml-8">
                    <button
                      class="toggle-button"
                      (click)="outboundSelect()"
                      (mouseenter)="outboundHover = true"
                      (mouseleave)="outboundHover = false"
                      [disabled]="form.get('isOutbound')?.disabled"
                    >
                      <img
                        class="select-image"
                        [src]="outboundImageSrc"
                        alt="Outbound"
                      />
                    </button>
                  </div>
                  <div class="col ml-8">
                    <button
                      class="toggle-button"
                      (click)="interfieldSelect()"
                      (mouseenter)="interfieldHover = true"
                      (mouseleave)="interfieldHover = false"
                      [disabled]="form.get('isInterfield')?.disabled"
                    >
                      <img
                        class="select-image"
                        [src]="interfieldImageSrc"
                        alt="Interfield"
                      />
                    </button>
                  </div>
                </div>
                <small
                  class="validation-control-error"
                  *ngIf="outboundNotSelected()"
                >
                  Interfield request detected, please add an outbound voyage
                </small>
                <small
                  class="validation-control-error"
                  *ngIf="nextButtonClicked && voyageTypeNotSelected()"
                >
                  Type of sailing is required
                </small>
                <small
                  class="validation-control-error"
                  *ngIf="nextButtonClicked && voyageTypeNotSelected()"
                  >Type of sailing is required</small
                >
              </div>
            </div>
          </form>
        </div>
      </p-tabPanel>

      <p-tabPanel
        [disabled]="isRequirementDisabled()"
        header="Requirement"
        class="mb-24"
      >
        <div class="form-section pt-10">
          <request-sailing-requirement
            [activityCategories]="filteredActivities"
            [units]="units ?? []"
            [inboundSelected]="form.controls.isInbound.value ?? false"
            [showErrors]="showErrors"
            [currentUser]="currentUser ?? null"
            [originalClients]="clients ?? []"
            [hasCompletedRequests]="hasCompletedRequests"
            (sendDataToParent)="handleDataFromChild($event)"
          ></request-sailing-requirement>
        </div>
      </p-tabPanel>

      <p-tabPanel
        [disabled]="isApprovalsDisabled()"
        header="Approvals"
        class="mb-24"
      >
        <div class="w-100">
          <request-sailing-approval
            [originalVessels]="vessels"
            [inboundSelection]="form.controls.isInbound.value"
            [outboundSelection]="form.controls.isOutbound.value"
            [showErrors]="showApprovalErrors"
            [hasCompletedRequests]="hasCompletedRequests"
            [isInboundRequestComplete]="isInboundRequestComplete"
            [isOutboundRequestComplete]="isOutboundRequestComplete"
            (approvalDataChanged)="approvalDataChanged($event)"
          ></request-sailing-approval>
        </div>
      </p-tabPanel>

      <p-tabPanel *ngIf="editMode" class="mb-24">
        <ng-template pTemplate="header">
          Comments
          <span *ngIf="addUserToComments" class="red-circle"></span>
        </ng-template>
        <ng-template pTemplate="body">
          <div class="w-100">
            <request-sailing-comments
              [requestSailingId]="requestSailingId"
              [comments]="sailingRequestUserComments"
              (dataChanged)="commentsDataChanged($event)"
            >
            </request-sailing-comments>
          </div>
        </ng-template>
      </p-tabPanel>
    </p-tabView>
  </div>

  <ng-template pTemplate="footer">
    <button class="btn-secondary" (click)="closeSideBar(false)" type="button">
      Cancel
    </button>

    <div
      *ngIf="
        selectedTabIndex === 0 ||
        (selectedTabIndex === 1 && !hasOnlyCliRoleInPlan())
      "
    >
      <button
        class="btn-primary"
        (click)="goToNextTab()"
        [disabled]="
          (selectedTabIndex === 0 &&
            (outboundNotSelected() ||
              !form.get('sailingRequestAssets')?.value?.length ||
              voyageTypeNotSelected() ||
              form.invalid)) ||
          (selectedTabIndex === 1 &&
            requirementComponent &&
            requirementComponent.form.invalid)
        "
        type="button"
      >
        Next
      </button>
    </div>
    <div
      *ngIf="
        (selectedTabIndex === 1 && hasOnlyCliRoleInPlan()) ||
        selectedTabIndex === 2
      "
    >
      <button
        [disabled]="loadingCreateEdit()"
        class="save btn-primary d-flex align-items-center"
        (click)="save()"
        type="submit"
      >
        <span [ngStyle]="{ opacity: loadingCreateEdit() ? '0' : '1' }">
          Save
        </span>
        <p-progressSpinner
          class="ml-10"
          [style.position]="'absolute'"
          [styleClass]="'small-spinner-style-btn-white'"
          *ngIf="loadingCreateEdit()"
        ></p-progressSpinner>
      </button>
    </div>
    <div
      *ngIf="editMode && selectedTabIndex === 3 && this.form.value.status === 3"
    >
      <button
        class="btn-negative-primary"
        (click)="deleteSailingRequest()"
        type="button"
      >
        Confirm Delete
      </button>
    </div>
    <div *ngIf="selectedTabIndex === 3">
      <button
        class="btn-primary"
        (click)="allowChildToSendComment()"
        type="button"
      >
        Send
      </button>
    </div>
  </ng-template>
</p-sidebar>
